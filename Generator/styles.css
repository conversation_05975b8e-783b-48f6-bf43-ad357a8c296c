/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    color: #1e293b;
    line-height: 1.6;
    min-height: 100vh;
}

/* Color Variables */
:root {
    --primary-color: #5e2e60;
    --secondary-color: #5f8132;
    --tertiary-color: #327881;
    --accent-color: #f59e0b;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --info-color: #3b82f6;
    --light-bg: #f8fafc;
    --white: #ffffff;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* App Container */
.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, var(--primary-color) 0%, #7c3aed 100%);
    color: white;
    padding: 1rem 0;
    box-shadow: var(--shadow-lg);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 2rem;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-shrink: 0;
}

.logo-image {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    box-shadow: var(--shadow-md);
}

.logo-text h1 {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
    color: white;
}

.logo-text .subtitle {
    font-size: 0.875rem;
    opacity: 0.9;
    font-weight: 400;
}

/* Main Navigation */
.main-nav {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.nav-tab {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.75rem 1.25rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    white-space: nowrap;
}

.nav-tab:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.nav-tab.active {
    background: var(--secondary-color);
    border-color: var(--secondary-color);
    box-shadow: var(--shadow-md);
}

.nav-tab i {
    font-size: 1rem;
}

/* Header Actions */
.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Main Content */
.main-content {
    flex: 1;
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem 1.5rem;
    width: 100%;
}

/* Tab Content */
.tab-content {
    width: 100%;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

/* Quotes Layout */
.quotes-layout {
    display: grid;
    grid-template-columns: 250px 1fr 350px;
    gap: 2rem;
    min-height: calc(100vh - 200px);
}

/* Sidebar */
.sidebar {
    background: var(--white);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: var(--shadow);
    height: fit-content;
    position: sticky;
    top: 120px;
}

.service-nav h3 {
    color: var(--gray-800);
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.service-nav ul {
    list-style: none;
}

.service-nav li {
    margin-bottom: 0.5rem;
}

.service-nav .nav-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: var(--gray-600);
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.2s ease;
    font-weight: 500;
}

.service-nav .nav-link:hover {
    background: var(--gray-50);
    color: var(--primary-color);
}

.service-nav .nav-link.active {
    background: linear-gradient(135deg, var(--primary-color), #7c3aed);
    color: white;
    box-shadow: var(--shadow-md);
}

.service-nav .nav-link i {
    font-size: 1rem;
    width: 20px;
    text-align: center;
}

/* Form Area */
.form-area {
    background: var(--white);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: var(--shadow);
}

/* Customer Section */
.customer-section {
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 2px solid var(--gray-100);
}

.customer-section h2 {
    color: var(--gray-800);
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.customer-section h2 i {
    color: var(--primary-color);
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 0.75rem 1rem;
    border: 2px solid var(--gray-200);
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.2s ease;
    background: var(--white);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(94, 46, 96, 0.1);
}

/* Services Section */
.services-section {
    margin-top: 2rem;
}

.service-category {
    display: none;
}

.service-category.active {
    display: block;
}

.service-category h3 {
    color: var(--gray-800);
    font-size: 1.375rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid var(--gray-100);
}

.service-category h3 i {
    color: var(--primary-color);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.service-subcategory {
    background: var(--gray-50);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid var(--gray-200);
}

.service-subcategory h4 {
    color: var(--gray-800);
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--gray-200);
}

.service-item {
    margin-bottom: 1rem;
    position: relative;
}

.service-label {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--white);
    border: 2px solid var(--gray-200);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.service-label:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
}

.service-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--gray-300);
    border-radius: 4px;
    position: relative;
    flex-shrink: 0;
    transition: all 0.2s ease;
}

.service-label input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.service-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    font-size: 12px;
}

.service-name {
    flex: 1;
    font-weight: 500;
    color: var(--gray-700);
}

.service-price {
    font-weight: 600;
    color: var(--primary-color);
    font-size: 1rem;
}

.service-label input[type="checkbox"]:checked ~ .service-name {
    color: var(--gray-800);
}

.service-label input[type="checkbox"]:checked ~ .service-price {
    color: var(--secondary-color);
}

/* Custom Input for Services */
.custom-input {
    margin-top: 0.75rem;
    padding: 0.75rem;
    background: var(--gray-50);
    border-radius: 6px;
    border: 1px solid var(--gray-200);
    display: none;
}

.service-item:has(input[type="checkbox"]:checked) .custom-input {
    display: block;
}

.custom-input label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--gray-600);
    margin-bottom: 0.5rem;
    display: block;
}

.custom-input input {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--gray-300);
    border-radius: 4px;
    font-size: 0.875rem;
}

/* Calculator Sidebar */
.calculator {
    background: var(--white);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: var(--shadow);
    height: fit-content;
    position: sticky;
    top: 120px;
}

.calculator-content h3 {
    color: var(--gray-800);
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.calculator-content h3 i {
    color: var(--primary-color);
}

.selected-services {
    margin-bottom: 1.5rem;
}

.selected-services h4 {
    color: var(--gray-700);
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.services-list {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid var(--gray-200);
    border-radius: 6px;
    padding: 0.75rem;
    background: var(--gray-50);
}

.services-list .service-item-calc {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--gray-200);
    font-size: 0.875rem;
}

.services-list .service-item-calc:last-child {
    border-bottom: none;
}

.services-list .service-name-calc {
    flex: 1;
    color: var(--gray-700);
}

.services-list .service-price-calc {
    font-weight: 600;
    color: var(--primary-color);
}

.no-services {
    color: var(--gray-500);
    font-style: italic;
    text-align: center;
    padding: 1rem;
}

/* Discount Section */
.discount-section {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: var(--gray-50);
    border-radius: 8px;
    border: 1px solid var(--gray-200);
}

.discount-section h4 {
    color: var(--gray-700);
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.discount-options {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
}

.discount-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
}

.discount-option:hover {
    background: var(--gray-100);
}

.discount-option input[type="radio"] {
    margin: 0;
}

/* Price Summary */
.price-summary {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: var(--gray-50);
    border-radius: 8px;
    border: 1px solid var(--gray-200);
}

.price-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    font-size: 0.875rem;
}

.price-row.total {
    border-top: 2px solid var(--gray-300);
    margin-top: 0.5rem;
    padding-top: 0.75rem;
    font-weight: 600;
    font-size: 1rem;
    color: var(--gray-800);
}

.price-row.discount-row {
    color: var(--danger-color);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    justify-content: center;
    white-space: nowrap;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), #7c3aed);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: var(--gray-100);
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
}

.btn-secondary:hover {
    background: var(--gray-200);
    border-color: var(--gray-400);
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-success:hover {
    background: #059669;
}

.btn-danger {
    background: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background: #dc2626;
}

.btn-generate {
    width: 100%;
    font-size: 1rem;
    padding: 1rem 1.5rem;
    background: linear-gradient(135deg, var(--secondary-color), #10b981);
}

.btn-generate:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

/* Clients Layout */
.clients-layout {
    background: var(--white);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: var(--shadow);
}

.clients-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--gray-100);
}

.clients-header h2 {
    color: var(--gray-800);
    font-size: 1.5rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.clients-header h2 i {
    color: var(--primary-color);
}

.clients-actions {
    display: flex;
    gap: 1rem;
}

/* Clients Stats */
.clients-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-radius: 12px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.2s ease;
}

.stat-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    background: linear-gradient(135deg, var(--primary-color), #7c3aed);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.stat-content h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-800);
    margin: 0;
}

.stat-content p {
    font-size: 0.875rem;
    color: var(--gray-600);
    margin: 0;
}

/* Clients Filters */
.clients-filters {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
    padding: 1rem;
    background: var(--gray-50);
    border-radius: 8px;
    border: 1px solid var(--gray-200);
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-group label {
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.filter-group input,
.filter-group select {
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--gray-300);
    border-radius: 6px;
    font-size: 0.875rem;
    background: var(--white);
}

/* Orders Layout */
.orders-layout {
    background: var(--white);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: var(--shadow);
}

/* Dashboard */
.dashboard {
    margin-bottom: 2rem;
}

.dashboard h2 {
    color: var(--gray-800);
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.dashboard h2 i {
    color: var(--primary-color);
}

.dashboard-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.dashboard-card {
    background: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-radius: 12px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.2s ease;
}

.dashboard-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.card-icon {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    background: linear-gradient(135deg, var(--primary-color), #7c3aed);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.card-content h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-800);
    margin: 0;
}

.card-content p {
    font-size: 0.875rem;
    color: var(--gray-600);
    margin: 0;
}

/* Orders Management */
.orders-management {
    border-top: 2px solid var(--gray-100);
    padding-top: 2rem;
}

.orders-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.orders-header h3 {
    color: var(--gray-800);
    font-size: 1.25rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.orders-header h3 i {
    color: var(--primary-color);
}

.orders-actions {
    display: flex;
    gap: 1rem;
}

/* Filters */
.filters {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
    padding: 1rem;
    background: var(--gray-50);
    border-radius: 8px;
    border: 1px solid var(--gray-200);
}

/* Tasks Container */
.tasks-container {
    display: grid;
    gap: 2rem;
}

.tasks-section h4 {
    color: var(--gray-800);
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--gray-200);
}

.tasks-list {
    display: grid;
    gap: 1rem;
}

/* Task Item */
.task-item {
    background: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-radius: 8px;
    padding: 1rem;
    transition: all 0.2s ease;
}

.task-item:hover {
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.task-title {
    font-weight: 600;
    color: var(--gray-800);
}

.task-status {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.task-status.pending {
    background: var(--warning-color);
    color: white;
}

.task-status.completed {
    background: var(--success-color);
    color: white;
}

.task-details {
    color: var(--gray-600);
    font-size: 0.875rem;
}

/* Invoices Layout */
.invoices-layout {
    background: var(--white);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: var(--shadow);
}

.invoices-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--gray-100);
}

.invoices-header h2 {
    color: var(--gray-800);
    font-size: 1.5rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.invoices-header h2 i {
    color: var(--primary-color);
}

/* Services Layout */
.services-layout {
    background: var(--white);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: var(--shadow);
}

.services-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--gray-100);
}

.services-header h2 {
    color: var(--gray-800);
    font-size: 1.5rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.services-header h2 i {
    color: var(--primary-color);
}

/* Settings Layout */
.settings-layout {
    background: var(--white);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: var(--shadow);
}

.settings-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--gray-100);
}

.settings-header h2 {
    color: var(--gray-800);
    font-size: 1.5rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.settings-header h2 i {
    color: var(--primary-color);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    backdrop-filter: blur(4px);
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: var(--white);
    border-radius: 12px;
    padding: 2rem;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: var(--shadow-xl);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--gray-200);
}

.modal-header h3 {
    color: var(--gray-800);
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--gray-500);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: var(--gray-100);
    color: var(--gray-700);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .quotes-layout {
        grid-template-columns: 200px 1fr 300px;
        gap: 1.5rem;
    }
}

@media (max-width: 1024px) {
    .quotes-layout {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .sidebar,
    .calculator {
        position: static;
    }

    .main-nav {
        justify-content: center;
    }

    .nav-tab {
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
    }

    .dashboard-cards {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }

    .clients-stats {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
}

@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .main-nav {
        flex-wrap: wrap;
        justify-content: center;
        gap: 0.25rem;
    }

    .nav-tab {
        padding: 0.5rem 0.75rem;
        font-size: 0.75rem;
    }

    .nav-tab span {
        display: none;
    }

    .main-content {
        padding: 1rem;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }

    .discount-options {
        grid-template-columns: 1fr;
    }

    .clients-header,
    .orders-header,
    .invoices-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .clients-actions,
    .orders-actions {
        flex-direction: column;
        width: 100%;
    }

    .filters {
        grid-template-columns: 1fr;
    }

    .dashboard-cards {
        grid-template-columns: 1fr;
    }

    .clients-stats {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .logo-text h1 {
        font-size: 1.25rem;
    }

    .logo-text .subtitle {
        font-size: 0.75rem;
    }

    .nav-tab {
        padding: 0.4rem 0.6rem;
    }

    .form-area,
    .sidebar,
    .calculator {
        padding: 1rem;
    }

    .modal-content {
        padding: 1rem;
        width: 95%;
    }

    .stat-card,
    .dashboard-card {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }

    .stat-icon,
    .card-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
}

/* Print Styles */
@media print {
    .header,
    .sidebar,
    .calculator,
    .nav-tab,
    .btn {
        display: none !important;
    }

    .main-content {
        padding: 0;
        max-width: none;
    }

    .form-area {
        box-shadow: none;
        border: 1px solid var(--gray-300);
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--gray-300);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.hidden { display: none !important; }
.visible { display: block !important; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 0.75rem; }
.mt-4 { margin-top: 1rem; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }
.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }
